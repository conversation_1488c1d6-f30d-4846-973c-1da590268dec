package org.example.dashboard.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.TrendingDown
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.rounded.Remove
import androidx.compose.material.icons.rounded.TrendingUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.core.utils.DateUtils
import org.example.dashboard.DaySpending
import org.example.dashboard.PreviousWeekSummary
import org.example.dashboard.formatPrice
import org.example.shared.util.getScreenWidth

@Composable
fun PreviousWeekSummary(
    previousWeekSummary: PreviousWeekSummary?,
    modifier: Modifier = Modifier
) {
    if (previousWeekSummary == null) return
    val screenWidth = remember { getScreenWidth() }
    val weekInfo by remember {
        mutableStateOf(DateUtils.getCurrentWeekInfoCurrentWeek())
    }

    /*Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {*/
    Column(
        modifier = Modifier
            .fillMaxWidth()
            /*.padding(20.dp)*/
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Poprzedni tydzień",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = previousWeekSummary.weekRange,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Overall status indicator
            val statusColor = when {
                previousWeekSummary.isOverBudget -> Color(0xFFEF4444) // Red
                previousWeekSummary.weekBudgetUsagePercent < 80f -> Color(0xFF10B981) // Green
                else -> Color(0xFFF59E0B) // Amber
            }

            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(statusColor, CircleShape)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Total spending vs budget
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                text = formatPrice(previousWeekSummary.totalSpending),
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Text(
                text = "${previousWeekSummary.weekBudgetUsagePercent.toInt()}%",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = if (previousWeekSummary.isOverBudget)
                    Color(0xFFEF4444) else Color(0xFF10B981)
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "z ${formatPrice(previousWeekSummary.totalBudget)}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Progress bar
        LinearProgressIndicator(
            progress = { (previousWeekSummary.weekBudgetUsagePercent / 100f).coerceAtMost(1f) },
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = if (previousWeekSummary.isOverBudget) Color(0xFFEF4444) else Color(0xFF10B981),
            trackColor = Color(0xFFE5E7EB),
        )

        Spacer(modifier = Modifier.height(20.dp))

        // Daily breakdown header
        Text(
            text = "Wydatki dzienne",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 12.dp)
        )

        // Compact grid layout for daily spendings
        DailySpendingsGrid(
            dailySpendings = previousWeekSummary.dailySpendings,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Quick stats summary
        QuickStatsSummary(
            previousWeekSummary = previousWeekSummary,
            modifier = Modifier.fillMaxWidth()
        )
    }
    /*}*/
}

@Composable
private fun DailySpendingsGrid(
    dailySpendings: List<DaySpending>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // First row - Mon, Tue, Wed (first 3 days)
        Row(){ dailySpendings.take(3).forEach { daySpending ->
                CompactDaySpendingCard(
                    daySpending = daySpending,
                    modifier = Modifier.weight(1f)
                )
            }
//        LazyRow(
//            horizontalArrangement = Arrangement.spacedBy(8.dp),
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            items(dailySpendings.take(3)) { daySpending ->
//                CompactDaySpendingCard(
//                    daySpending = daySpending,
//                    modifier = Modifier.weight(1f)
//                )
//            }
        }

        // Second row - Thu, Fri (next 2 days)
        if (dailySpendings.size > 3) {
            Row(){ dailySpendings.drop(3).take(2).forEach { daySpending ->
                CompactDaySpendingCard(
                    daySpending = daySpending,
                    modifier = Modifier.weight(1f)
                )
            }
//            LazyRow(
//                horizontalArrangement = Arrangement.spacedBy(8.dp),
//                modifier = Modifier.fillMaxWidth()
//            ) {
//                items(dailySpendings.drop(3).take(2)) { daySpending ->
//                    CompactDaySpendingCard(
//                        daySpending = daySpending,
//                        modifier = Modifier.weight(1f)
//                    )
//                }
            }
        }

        // Third row - Weekend (last 2 days)
        if (dailySpendings.size > 5) {
            Row(){ dailySpendings.drop(5).forEach { daySpending ->
                CompactDaySpendingCard(
                    daySpending = daySpending,
                    modifier = Modifier.weight(1f)
                )
            }
//            LazyRow(
//                horizontalArrangement = Arrangement.spacedBy(8.dp),
//                modifier = Modifier.fillMaxWidth()
//                /*modifier = Modifier.fillMaxWidth()
//                    .weight(1f) // to sprawia ze nie widac w ogole tego wiersza*/
//            ) {
//                items(dailySpendings.drop(5)) { daySpending ->
//                    CompactDaySpendingCard(
//                        daySpending = daySpending,
//                        /*modifier = Modifier.weight(1f)*/
//                    )
//                }
            }
        }
    }
}

@Composable
private fun CompactDaySpendingCard(
    daySpending: DaySpending,
    modifier: Modifier = Modifier,

) {
//    val screenWidth = remember { getScreenWidth() }
    Card(
//        modifier = modifier.width((screenWidth / 3).dp),
        modifier = modifier,
        /*modifier = modifier.weight(1f), // nie dziaua bo nie w zwyklym row?
        A, nie dlatego.
        A dlatego bo weight juz jest dodany do modifier przy wywolaniu tego komponentu w LazyRow*/
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF9FAFB)
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // Day name and trend icon
            Row(
                modifier = Modifier.fillMaxWidth(),
                /*modifier = Modifier.fillMaxWidth().weight(1f),*/
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = daySpending.dayName,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // Trend icon
                val (icon, iconColor) = when {
                    daySpending.budgetUsagePercent > 100f -> Icons.Rounded.TrendingUp to Color(
                        0xFFEF4444
                    )

                    daySpending.budgetUsagePercent > 50f -> Icons.Rounded.TrendingUp to Color(
                        0xFFF59E0B
                    )

                    else -> Icons.Rounded.Remove to Color(0xFF10B981)
                }

                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(12.dp)
                )
            }

            // Amount
            Text(
                text = formatPrice(daySpending.spending),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            // Percentage
            val percentageColor = when {
                daySpending.budgetUsagePercent > 100f -> Color(0xFFEF4444)
                daySpending.budgetUsagePercent > 50f -> Color(0xFFF59E0B)
                else -> Color(0xFF10B981)
            }

            Text(
                text = "${daySpending.budgetUsagePercent.toInt()}%",
                fontSize = 12.sp,
                color = percentageColor
            )

            // Mini progress bar
            val progressColor = when {
                daySpending.budgetUsagePercent > 100f -> Color(0xFFEF4444)
                daySpending.budgetUsagePercent > 50f -> Color(0xFFF59E0B)
                else -> Color(0xFF10B981)
            }

            LinearProgressIndicator(
                progress = { (daySpending.budgetUsagePercent / 200f).coerceAtMost(1f) }, // Scale down for visual
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp)),
                color = progressColor,
                trackColor = Color.White,
            )
        }
    }
}

@Composable
private fun QuickStatsSummary(
    previousWeekSummary: PreviousWeekSummary,
    modifier: Modifier = Modifier
) {
    // Calculate quick stats
    val highestDay = previousWeekSummary.dailySpendings.maxByOrNull { it.budgetUsagePercent }
    val daysOverBudget = previousWeekSummary.dailySpendings.count { it.isOverBudget }

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp)
                .background(
                    color = Color(0xFFF9FAFB),
                    shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
                )
                .padding(1.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Najwyższy: ${highestDay?.dayName ?: "N/A"} (${highestDay?.budgetUsagePercent?.toInt() ?: 0}%)",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = "Dni powyżej planu: $daysOverBudget",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
