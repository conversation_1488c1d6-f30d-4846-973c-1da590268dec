package org.example.dashboard

import org.example.core.utils.DateUtils
import org.example.shared.component.DateFilter

object BudgetManager {
    private const val WEEKLY_BUDGET = 95700L // 957,00
    private const val DAILY_BASE_BUDGET = WEEKLY_BUDGET / 7 // ~136,71 bazowy budżet dzienny

    /**
     * Oblicza budżet dla danego filtra daty
     */
    fun calculateBudgetForDateFilter(filter: DateFilter): Long {
        return when (filter) {
            is DateFilter.ThisWeek -> WEEKLY_BUDGET
            is DateFilter.Today -> DAILY_BASE_BUDGET // Bazowy budżet - będzie nadpisany przez adaptacyjny
            is DateFilter.ThisMonth -> {
                val daysInMonth = getCurrentMonthDays()
                DAILY_BASE_BUDGET * daysInMonth
            }

            is DateFilter.LastMonth -> {
                val daysInLastMonth = getLastMonthDays()
                DAILY_BASE_BUDGET * daysInLastMonth
            }

            is DateFilter.Custom -> calculateCustomFilterBudget(filter)
            DateFilter.All -> Long.MAX_VALUE // Bez limitu dla "wszystkich"
            DateFilter.Last7Days -> WEEKLY_BUDGET
            DateFilter.PreviousWeek -> WEEKLY_BUDGET
            DateFilter.ThisAndPreviousweek -> WEEKLY_BUDGET * 2
        }
    }

    /**
     * * !!! This documentation may be outdated or incorrect !!!
     * Oblicza adaptacyjny budżet dzienny na podstawie dotychczasowych wydatków w tygodniu
     *
     * @param weeklySpentSoFar - wydane środki od początku tygodnia do wczoraj
     * @param dayOfWeek - który to dzień tygodnia (1=poniedziałek, 7=niedziela)
     * @param weeklyBudget - całkowity budżet tygodniowy
     * @return adaptacyjny budżet na dzisiaj
     */
    fun calculateAdaptiveDailyBudget(
        weeklySpentSoFar: Long,
        dayOfWeek: Int,
        weeklyBudget: Long = WEEKLY_BUDGET
    ): Long {
        println("DEBUG BudgetManager: weeklySpentSoFar=$weeklySpentSoFar, dayOfWeek=$dayOfWeek, weeklyBudget=$weeklyBudget")

        val remainingDays = 8 - dayOfWeek

        println("DEBUG BudgetManager: remainingDays=$remainingDays")

        if (remainingDays <= 0) {
            println("DEBUG BudgetManager: No remaining days, returning 0")
            return 0L // Koniec tygodnia
        }

        val remainingWeekBudget = weeklyBudget - weeklySpentSoFar

        println("DEBUG BudgetManager: remainingBudget=$remainingWeekBudget")

        // Jeśli przekroczono budżet, nadal można wydawać, ale odpowiednio pomniejszone kwoty
        val result = if (remainingWeekBudget <= 0) {
            val emergencyBudget =
                DAILY_BASE_BUDGET / 4 // 25% bazowego budżetu jako "emergency budget"
            println("DEBUG BudgetManager: Budget exceeded, emergency budget: $emergencyBudget")
            emergencyBudget
        } else {
            // Podziel pozostały budżet równomiernie na pozostałe dni
            val adaptiveBudget = remainingWeekBudget / remainingDays
            println("DEBUG BudgetManager: Adaptive budget: $adaptiveBudget")
            adaptiveBudget
        }

        println("DEBUG BudgetManager: Final result: $result")
        return result
    }

    /**
     * Oblicza postęp budżetu z uwzględnieniem adaptacyjności
     */
    fun calculateBudgetProgress(
        currentSpending: Long,
        originalBudget: Long,
        adaptiveBudget: Long? = null
    ): BudgetProgress {
        val budgetToUse = adaptiveBudget ?: originalBudget
        val progress = if (budgetToUse > 0) {
            (currentSpending.toFloat() / budgetToUse.toFloat()).coerceAtMost(1.5f) // Cap at 150%
        } else {
            0f
        }

        return BudgetProgress(
            progress = progress,
            isExceeded = currentSpending > budgetToUse,
            isNearLimit = progress > 0.8f && !currentSpending.equals(budgetToUse),
            remainingBudget = budgetToUse - currentSpending,
            usedBudget = currentSpending,
            totalBudget = budgetToUse
        )
    }

    /**
     * Oblicza sugerowane wydatki na pozostałą część dnia
     * na podstawie historycznych wzorców (opcjonalne - do przyszłej implementacji)
     */
    fun calculateRecommendedSpending(
        currentDaySpent: Long,
        adaptiveDailyBudget: Long,
        timeOfDay: Float // 0.0 - 1.0 (gdzie 1.0 = koniec dnia)
    ): SpendingRecommendation {
        val expectedSpendingByNow = (adaptiveDailyBudget * timeOfDay).toLong()
        val remainingBudgetForDay = adaptiveDailyBudget - currentDaySpent

        return SpendingRecommendation(
            recommendedRemaining = remainingBudgetForDay,
            isOnTrack = currentDaySpent <= expectedSpendingByNow,
            variance = currentDaySpent - expectedSpendingByNow
        )
    }

    private fun calculateCustomFilterBudget(filter: DateFilter.Custom): Long {
        if (filter.startDate == null || filter.endDate == null) {
            return WEEKLY_BUDGET
        }

        try {
            val startTimestamp = DateUtils.isoStringToTimestamp(filter.startDate!!)
            val endTimestamp = DateUtils.isoStringToTimestamp(filter.endDate!!)

            val diffInMillis = endTimestamp - startTimestamp
            val diffInDays =
                (diffInMillis / (1000 * 60 * 60 * 24)).toInt() + 1 // +1 żeby uwzględnić dzień końcowy

            return DAILY_BASE_BUDGET * maxOf(1, diffInDays)
        } catch (e: Exception) {
            return WEEKLY_BUDGET
        }
    }

    private fun getCurrentMonthDays(): Int {
        return DateUtils.getCurrentMonthDays()
    }

    private fun getLastMonthDays(): Int {
        return DateUtils.getLastMonthDays()
    }
}

/**
 * Data class reprezentujący szczegółowy postęp budżetu
 */
data class BudgetProgress(
    val progress: Float,
    val isExceeded: Boolean,
    val isNearLimit: Boolean,
    val remainingBudget: Long,
    val usedBudget: Long,
    val totalBudget: Long
)

/**
 * Data class z rekomendacjami wydatków
 */
data class SpendingRecommendation(
    val recommendedRemaining: Long,
    val isOnTrack: Boolean,
    val variance: Long // dodatnia = wydajesz za dużo, ujemna = zostało Ci więcej
)