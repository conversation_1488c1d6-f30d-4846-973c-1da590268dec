package org.example.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.DayOfWeek
import org.example.core.domain.model.Category
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.shared.component.DateFilter
import org.example.shared.component.DateFilterUtils
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils

data class ReceiptData(
    val id: String,
    val purchaseDate: String,
    val receiptSum: String = "0,00",
)

data class CategorySpending(
    val categoryName: String,
    val categorySum: Long
)

data class DashboardUiState(
    val receipts: List<ReceiptData> = emptyList(),
    val budget: Long = 0L,
    val totalSpending: Long = 0,
    val categorySpendings: List<CategorySpending> = emptyList(),
    val currentFilter: DateFilter = DateFilter.ThisWeek,
    val isFilterExpanded: Boolean = false,
    val availableFilters: List<DateFilter> = DateFilter.getDefaultFilters(),
    val availableCategories: List<Category> = emptyList(),
    val selectedDate: LocalDate? = null,
    val remainingBudget: Long = 0L,
    val budgetProgress: Float = 0f,
    val isBudgetExceeded: Boolean = false,
    // Nowe pola dla adaptacyjnego budżetu
    val adaptiveDailyBudget: Long = 0L,
    val budgetProgressDetails: BudgetProgress? = null,
    val spendingRecommendation: SpendingRecommendation? = null,
    val isAdaptiveBudgetActive: Boolean = false
)

class DashboardViewModel(
    private val getReceiptsUseCase: GetReceiptsUseCase,
    private val getTypesAndCategoriesUseCase: GetTypesAndCategoriesUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        observeReceipts()
        observeCategories()
    }

    private fun observeCategories() {
        viewModelScope.launch {
            getTypesAndCategoriesUseCase.getCategories().collect { categories ->
                _uiState.update { it.copy(availableCategories = categories) }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun observeReceipts() {
        viewModelScope.launch {
            _uiState
                .map { it.currentFilter }
                .distinctUntilChanged()
                .flatMapLatest { filter ->
                    val dateRange = DateFilterUtils.getDateRange(filter)
                    getReceiptsUseCase.getReceiptsWithDateFilter(
                        dateRange.startDate,
                        dateRange.endDate
                    )
                }
                .collect { receipts ->
                    val currentFilter = _uiState.value.currentFilter
                    updateBudgetCalculations(receipts, currentFilter)
                }
        }
    }

    private suspend fun updateBudgetCalculations(
        receipts: List<Receipt>,
        currentFilter: DateFilter
    ) {
        println("DEBUG updateBudgetCalculations: filter=$currentFilter")

        val baseBudget = BudgetManager.calculateBudgetForDateFilter(currentFilter)
        val totalSpending = calculateTotalSpending(receipts)

        println("DEBUG updateBudgetCalculations: baseBudget=$baseBudget, totalSpending=$totalSpending")

        // Sprawdź czy to jest filtr dzisiejszego dnia lub obecnego tygodnia
        val isAdaptiveBudgetActive = shouldUseAdaptiveBudget(currentFilter)
        val isCurrentDayFilter = isCurrentDay(currentFilter)

        println("DEBUG updateBudgetCalculations: isAdaptiveBudgetActive=$isAdaptiveBudgetActive, isCurrentDayFilter=$isCurrentDayFilter")

        // Dla adaptacyjnego budżetu oblicz specjalną wartość, dla innych użyj bazowego
        val effectiveBudget = if (isAdaptiveBudgetActive && isCurrentDayFilter) {
            calculateAdaptiveBudgetForToday()
        } else {
            baseBudget
        }

        println("DEBUG updateBudgetCalculations: effectiveBudget=$effectiveBudget")

        val budgetProgressDetails = BudgetManager.calculateBudgetProgress(
            currentSpending = totalSpending,
            originalBudget = baseBudget,
            adaptiveBudget = if (isAdaptiveBudgetActive) effectiveBudget else null
        )

        val spendingRecommendation = if (isAdaptiveBudgetActive && isCurrentDayFilter) {
            val timeOfDay = calculateTimeOfDay()
            BudgetManager.calculateRecommendedSpending(
                currentDaySpent = totalSpending,
                adaptiveDailyBudget = effectiveBudget,
                timeOfDay = timeOfDay
            )
        } else null

        _uiState.update { currentState ->
            currentState.copy(
                receipts = receipts.map {
                    ReceiptData(
                        id = it.id,
                        purchaseDate = it.purchaseDate,
                        receiptSum = formatPrice(it.receiptSum ?: 0)
                    )
                },
                budget = effectiveBudget, // To jest kluczowe - przekazuj rzeczywisty budżet
                totalSpending = totalSpending,
                categorySpendings = calculateCategorySpendings(receipts),
                remainingBudget = budgetProgressDetails.remainingBudget,
                budgetProgress = budgetProgressDetails.progress,
                isBudgetExceeded = budgetProgressDetails.isExceeded,
                adaptiveDailyBudget = if (isAdaptiveBudgetActive) effectiveBudget else baseBudget,
                budgetProgressDetails = budgetProgressDetails,
                spendingRecommendation = spendingRecommendation,
                isAdaptiveBudgetActive = isAdaptiveBudgetActive
            )
        }

        println("DEBUG updateBudgetCalculations: Updated UI state with budget=$effectiveBudget")
    }

    private suspend fun calculateAdaptiveBudgetForToday(): Long {
        val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
        val todayTimestamp = DateUtils.getCurrentTimestamp()
        val startOfWeek = DateUtils.getStartOfWeek(todayTimestamp)

        // Jeśli to poniedziałek, zwróć bazowy budżet
        if (today.dayOfWeek == DayOfWeek.MONDAY) {
            println("DEBUG: Monday - returning base budget")
            return BudgetManager.calculateBudgetForDateFilter(DateFilter.Today)
        }

        // Pobierz wydatki od początku tygodnia do wczoraj (WYŁĄCZNIE)
        val yesterdayEnd = DateUtils.getYesterdayEnd()
        val weeklySpentSoFar = getSpendingForDateRange(startOfWeek, yesterdayEnd)
        val dayOfWeek = today.dayOfWeek.ordinal + 1 // kotlinx-datetime: MONDAY = 0, więc +1 = 1-7

        println("DEBUG: weeklySpentSoFar=$weeklySpentSoFar, dayOfWeek=$dayOfWeek")

        val adaptiveBudget = BudgetManager.calculateAdaptiveDailyBudget(
            weeklySpentSoFar = weeklySpentSoFar,
            dayOfWeek = dayOfWeek
        )

        println("DEBUG: Adaptive budget calculated: $adaptiveBudget")
        return adaptiveBudget
    }

    private suspend fun getSpendingForDateRange(startTimestamp: Long, endTimestamp: Long): Long {
        return getReceiptsUseCase.getReceiptsWithDateFilter(
            DateUtils.timestampToIsoString(startTimestamp),
            DateUtils.timestampToIsoString(endTimestamp)
        ).first().let { receipts ->
            calculateTotalSpending(receipts)
        }
    }

    private fun isCurrentDay(filter: DateFilter): Boolean {
        return when (filter) {
            is DateFilter.Today -> true
            is DateFilter.Custom -> {
                // Sprawdź czy custom filter to dzisiaj
                if (filter.startDate != null) {
                    val todayTimestamp = DateUtils.getCurrentTimestamp()
                    val filterTimestamp = DateUtils.isoStringToTimestamp(filter.startDate!!)
                    DateUtils.isSameDay(todayTimestamp, filterTimestamp)
                } else {
                    false
                }
            }

            else -> false
        }
    }

    private fun shouldUseAdaptiveBudget(filter: DateFilter): Boolean {
        return when (filter) {
            is DateFilter.Today -> true
            is DateFilter.ThisWeek -> false // Dla tygodnia używamy stałego budżetu
            is DateFilter.Custom -> {
                // Dla custom sprawdź czy to jeden dzień w obecnym tygodniu
                filter.startDate != null && filter.endDate != null &&
                        isWithinCurrentWeek(filter.startDate!!) &&
                        isSingleDayRange(filter.startDate!!, filter.endDate!!)
            }

            else -> false
        }
    }

    private fun isWithinCurrentWeek(dateString: String): Boolean {
        return try {
            val targetTimestamp = DateUtils.isoStringToTimestamp(dateString)
            val todayTimestamp = DateUtils.getCurrentTimestamp()
            val startOfWeek = DateUtils.getStartOfWeek(todayTimestamp)
            val endOfWeek = DateUtils.getEndOfWeek(todayTimestamp)

            targetTimestamp >= startOfWeek && targetTimestamp <= endOfWeek
        } catch (e: Exception) {
            false
        }
    }

    private fun isSingleDayRange(startDateString: String, endDateString: String): Boolean {
        return try {
            val startTimestamp = DateUtils.isoStringToTimestamp(startDateString)
            val endTimestamp = DateUtils.isoStringToTimestamp(endDateString)
            DateUtils.isSingleDayRange(startTimestamp, endTimestamp)
        } catch (e: Exception) {
            false
        }
    }

    private fun calculateTimeOfDay(): Float {
        return DateUtils.getCurrentTimeOfDay()
    }

    // Pozostałe metody bez zmian...
    private fun calculateTotalSpending(receipts: List<Receipt>): Long {
        val products = receipts.flatMap { it.products }
        return products.sumOf { it.totalInCents }
    }

    private fun calculateCategorySpendings(receipts: List<Receipt>): List<CategorySpending> {
        val products = receipts.flatMap { it.products }
        val categorySums = products.groupBy { it.category }
            .mapValues { entry ->
                entry.value.sumOf { it.totalInCents }
            }
        return categorySums.map { (category, sum) ->
            CategorySpending(category, sum)
        }
    }

    fun onDaySelected(selectedDate: LocalDate) {
        val currentState = _uiState.value

        if (currentState.selectedDate == selectedDate) {
            _uiState.update {
                it.copy(
                    currentFilter = DateFilter.ThisWeek,
                    selectedDate = null,
                    isFilterExpanded = false
                )
            }
        } else {
            val startOfDay = DateUtils.localDateToTimestamp(selectedDate)
            val endOfDay = DateUtils.getEndOfDay(startOfDay)

            val dayFilter = DateFilter.Custom(
                startDate = DateUtils.timestampToIsoString(startOfDay),
                endDate = DateUtils.timestampToIsoString(endOfDay)
            )

            _uiState.update {
                it.copy(
                    currentFilter = dayFilter,
                    selectedDate = selectedDate,
                    isFilterExpanded = false
                )
            }
        }
    }

    /**
     * Metoda do ręcznego przeliczenia budżetu (np. po dodaniu nowego wydatku)
     */
    fun recalculateBudget() {
        viewModelScope.launch {
            val currentFilter = _uiState.value.currentFilter
            val dateRange = DateFilterUtils.getDateRange(currentFilter)
            val receipts = getReceiptsUseCase.getReceiptsWithDateFilter(
                dateRange.startDate,
                dateRange.endDate
            ).first()

            updateBudgetCalculations(receipts, currentFilter)
        }
    }
}

fun formatPrice(priceInCents: Long, separator: String = ","): String {
    val baseUnit = priceInCents / 100
    val subunit = priceInCents % 100
    return "$baseUnit$separator${subunit.toString().padStart(2, '0')}"
}
