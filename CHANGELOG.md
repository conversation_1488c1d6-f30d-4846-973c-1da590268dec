## [Unreleased] - 2025-08-09
### ✨ Added
- **Dashboard**
  - New **budget tracking** with `BudgetManager`:
    - Supports "This Week", "Today", and single-day selections.
    - Displays spent, remaining, and overspent amounts.
    - Progress bar with yellow (normal) and red (overflow) states.
  - `CurrentWeekBudget` component replaces `CurrentWeekCalendar` for a more integrated budget view.
  - Extracted `DonutChart` and `HorizontalBarChartItem` into separate components; reduced donut size to 150dp.
- **Receipts**
  - Full **delete receipt** functionality:
    - Added `DeleteReceiptUseCase`.
    - Integrated delete flow with `ReceiptManager`, `AddReceiptViewModel`, and UI.
    - Added confirmation dialog for deletion.
- **Shared**
  - Added new date filters: `PreviousWeek` and `ThisAndPreviousWeek`.

### 🛠 Changed
- **Dashboard UI**
  - Replaced `Divider` with `HorizontalDivider` for consistency.
  - Improved day selection logic in `DashboardViewModel`.

### 🗑 Removed
- Replaced `CurrentWeekCalendar` in Dashboard with `CurrentWeekBudget`.


## [Unreleased] - 2025-08-06
### ✨ Added
- **Receipts**
  - Added ability to duplicate receipts:
    - New option in overflow menu ("Zduplikuj") to create a copy with a new ID and updated metadata.
    - The copied receipt has reset image, adjusted name (to indicate duplication), and fresh save dates.

- **ViewModel / UI**
  - Added `DuplicateReceipt` and `DeleteReceipt` events to `AddReceiptEvent`.
  - Added confirmation dialogs for clearing and deleting receipts.
  - UI updates in `AddReceiptContent` for new menu options.

- **Domain / Data**
  - Introduced `DuplicateReceiptUseCase`.
  - Implemented `duplicateReceipt` in `ReceiptRepositoryImpl`.

### 🛠 Changed
- **Dependency Injection**
  - Registered `DuplicateReceiptUseCase` in DI (Koin).


## [Unreleased] - 2025-08-06
### ✨ Added
- **Dashboard**
  - Introduced `CurrentWeekCalendar` component for selecting a day in the current week.
  - New date selection logic via `DashboardViewModel.onDaySelected()` with support for resetting selection.
  - Integrated new calendar UI into `DashboardScreen` (replacing previous `DateFilterComponent`).

- **Utilities**
  - Added calendar utilities in `core/utils/DateUtils.kt`:
    - Week number calculation.
    - Day and month data for the current week.


## [Unreleased] - 2025-08-03
### 🛠 Changed
- **Android Document Scanner**
  - Introduced `DocumentScannerState` singleton to manage scanner-related state.
  - `AndroidDocumentScanner` now uses `DocumentScannerState` instead of static variables, improving testability and reducing memory leak risk.
  - File save operations moved to IO dispatcher for better threading.
  - Removed unused debug print statements in `MainActivity`.

  
## [Unreleased] - 2025-08-03
### ✨ Added
- **Image File Management**
  - New `ImageFileManager` interface for unified receipt image handling.
  - Android implementation (`ReceiptImageManager`) manages temp/final files and cleans up old temp files.
  - iOS implementation added as a no-op (file lifecycle handled by iOS).
  - Integrated with `ReceiptManager` to finalize image file paths when saving receipts.

- **Simplified Process Death Recovery (Android)**
  - Refactored `AndroidDocumentScanner` to use `ReceiptImageManager` for file handling.
  - Added `CheckForRecoveredScan` composable to restore scanned image after process death and trigger OCR.

### 🛠 Changed
- Removed `DocumentScannerRecoveryManager` and redundant SharedPreferences state handling.
- Removed `AddReceiptEvent.RecoverFromProcessDeath`; recovery now handled fully in UI layer.
- Updated DI to provide `ImageFileManager`.



## [Unreleased] - 2025-08-03
### ✨ Added
- **Scan Process Death Recovery (Android)**
  - Document scanning now survives process death caused by the system.
  - Scan state is persisted in SharedPreferences and restored on app restart.
  - Recovered scan results are processed automatically with OCR.

### 🛠 Changed
- `AndroidDocumentScanner` and `MainActivity` updated to use process death recovery logic.
- `AddReceiptViewModel` and `AddReceiptContent` updated to support restored scans.


## [Unreleased] - 2025-08-03
### ♻️ Refactor
- **Android Document Scanning**
  - Migrated from deprecated `startActivityForResult/onActivityResult` to **`ActivityResultLauncher`** API.
  - `AndroidDocumentScanner` now uses a provided launcher via `setLauncher()`.
  - `MainActivity` handles activity results using the new `registerForActivityResult` approach.

## [Unreleased] - 2025-08-02
### ✨ Features
- **Navigation / Home Screen**
  - Added a **Floating Action Button (FAB)** with a dropdown menu for **"Add receipt"** and **"Scan receipt"** actions on the `HomeGraphScreen`.
  - `AddReceiptScreen` is now a **standalone navigable destination**, improving navigation clarity.

### ♻️ Changes
- Removed **"Add Receipt"** from the bottom bar destinations.
- Updated `HomeGraphScreen` to accept navigation callbacks (`navigateToAddReceipt`, `navigateToAddReceiptWithScan`, `navigateToEditReceipt`).
- Refactored `NavGraph`:
  - Added dedicated routes for `Screen.AddReceipt`, `Screen.AddReceiptWithScan`, and `Screen.EditReceipt`.
  - These routes now pass `navigateBack` and `startWithScan` to `AddReceiptScreen`.

### 🖼️ UI/UX
- `AddReceiptContent` now has a **back arrow in the TopAppBar**.
- Added automatic scanner launch when `startWithScan` is `true`.

### 📝 Other
- Removed debug print statements in `DashboardViewModel`.

## [Unreleased] - 2025-07-29
### ✨ Features
- **Dashboard Visualization**:
  - Added `DonutChart` composable to display category spending as a donut chart with interactive selection.
  - Added `HorizontalBarChartItem` composable to display spending per category as horizontal bars with percentages and sums.
  - `DashboardViewModel` now fetches categories via `GetTypesAndCategoriesUseCase` and calculates `totalSpending`.

### ♻️ Changes
- Updated `DashboardUiState`:
  - Added `totalSpending` and `availableCategories`.
  - `categorySpendings` now uses `Long` for sums instead of `String`.
- Updated layout of `DashboardScreen` to a `LazyColumn` and removed old `CategorySum` composable.

### 🗂 Dependency Injection
- Provided `GetTypesAndCategoriesUseCase` to `DashboardViewModel` in Koin module.

### 📝 Other
- Minor documentation update in `ReceiptDao.kt`.


## [Unreleased] - 2025-07-28
### ✨ Features
- **Dashboard**:
  - Added new `DashboardScreen` showing spending per category with date filtering.
  - `DashboardViewModel` handles receipt fetching, filter state, and spending calculations.
  - Introduced new data classes `ReceiptData` and `CategorySpending` for UI display.

### ♻️ Refactoring / Improvements
- Moved `DateFilterComponent`, `DateFilter`, and `DateFilterUtils` to `shared/component` for reuse across features.
- `ReceiptListScreen` and `ReceiptListViewModel` updated to use shared date filter components.

### 🏠 Navigation
- `HomeGraphScreen`:
  - Start destination changed from `Screen.ReceiptList` to `Screen.Dashboard`.
  - Added `DashboardScreen` to navigation graph.

### 🗂 Dependency Injection
- Added `DashboardViewModel` to Koin modules.
- Updated `di/build.gradle.kts` to include `feature:dashboard`.

### ⚙️ Build
- `shared/build.gradle.kts` now includes `compose.materialIconsExtended`, `kotlinx.datetime`, and `:core` dependencies.


## [Unreleased] - 2025-07-28

### ✨ Features

- **OCR highlighting persistence**:
  - Added support for saving and loading OCR highlighting data (bounding boxes and text lines) for products.
  - The app can now reconstruct the exact text areas used during OCR, enabling future features like text highlighting on receipt images.

### 🛠 Refactoring / Improvements

- Moved `MyBoundingBox`, `MyTextBlock`, and `GroupedTextLine` models to `core/domain/model`.
- `Product` domain model now includes an optional `ocrGroupedTextLine` field.
- `ProductEntity.toDomainModel()` updated to accept a `GroupedTextLine`.

### 🗄 Database Changes

- Schema version bumped to **4**.
- Added new entities (`MyBoundingBoxEntity`, `MyTextBlockEntity`, `GroupedTextLineEntity`) and their DAOs.
- `HighlightingService` handles persistence and retrieval of OCR highlighting data.
- Removed legacy `Migration.kt` (MIGRATION_1_2); migrations now rely on version bumps.

### 🔄 Repository & Data Layer

- `ReceiptRepositoryImpl`:
  - Saves and loads OCR highlighting when saving or fetching receipts.
  - Deletes previous highlighting data for products on updates.

### 🎨 UI / Feature Updates

- Updated `ReceiptManager`, `TextRecognizer`, and related components to integrate OCR highlighting.
- UI elements (`ProductCard`, `AddReceiptContent`, etc.) now support highlighting-aware data.


## [Unreleased] - 2025-07-27
### ✨ Features
- Add dashboard feature module. Include this new module in feature home and navigation modules

## [Unreleased] - 2025-07-27

### ♻️ Refactoring
- **ColorPalette:** Updated default colors for Needs, Fun, and Limit to more distinct, stronger variants.
- **DatabaseInitializer:** Adjusted `getDefaultTypes()` and `getDefaultCategories()` to use new color values.
- **ReceiptListViewModel:** Fixed sum calculation to use `product.totalInCents` directly (no redundant multiplication by `qty`).

### 🔧 Other Changes
- **AndroidManifest:** Disabled app backup by setting `android:allowBackup="false"`.


## [Unreleased] - 2025-07-27

### ✨ Features
- **AddReceiptScreen:** Added ability to expand or collapse all product cards at once with a single button.
- **ProductCard:** Now controlled externally via `isExpanded` and `onExpandedChange` props.

### 🛠 Improvements
- Expansion states (`expandedStates`) are now centrally managed in `AddReceiptContent`.
- Expand/collapse all button is animated for better UX.


## [Unreleased] - 2025-07-20
### ✨ Features

- **Receipt editing**: Users can now edit existing receipts from the receipt list screen.
- **Edit navigation flow**: Added new route and navigation handling for editing receipts via `Screen.EditReceipt(receiptId)`.

### 🛠 Refactoring / Improvements

- **UI state expanded**: `AddReceiptUiState` and screen logic now differentiate between create and edit modes.
- **DAO behavior**: `ProductDao.getProductsByReceiptId` no longer sorts products by name, preserving original order.
- **Dynamic UI text**: Form titles and button labels adapt based on mode (e.g., "Zapisz" vs "Aktualizuj").

### 🧭 Navigation & DI

- **Navigation**:
  - `HomeGraphScreen` handles routing to edit mode.
  - `NavGraph.kt` contains commented-out alternative EditReceipt composable.
- **Dependency Injection**:
  - Registered `UpdateReceiptUseCase` in Koin.
  - Updated `ReceiptManager` with use cases needed for editing.


## [Unreleased] - 2025-07-20
### ✨ Features

- **Date filtering added**: Receipt list screen now supports predefined and custom date filtering via a new UI component and logic layer.
- **New component**: `DateFilterComponent` allows users to choose date filters like "Today", "This week", or select a custom range.

### 🛠 Refactoring / Architecture

- **Centralized date handling**: Introduced `DateUtils` with formatting, conversion, and range utilities in `core`.
- **Database date storage changed**: Refactored `ProductEntity` and `ReceiptEntity` to store `Long` timestamps instead of `String` dates.
- **ViewModel cleanup**: Removed manual date formatting from `ReceiptListViewModel`, now delegated to `DateUtils`.

### 🔧 Configuration

- **Database schema version bumped to v3** due to date field changes.
- **Dependencies**:
  - Added `kotlinx-datetime` to `core` and `data` modules.
  - Changed OpenAI model to `"gpt-4o"` in request builder.


## [Unreleased] - 2025-07-20
### ✨ Features

- **Receipt management added**: Introduced saving receipts with products to database and displaying them in a list.
- **New feature module**: `feature/receiptlist` with `ReceiptListScreen` and segmented receipt progress bar UI.
- **Use cases**: `SaveReceiptUseCase`, `GetReceiptsUseCase`, and `GetProductsUseCase` added for domain logic.

### 🛠 Refactoring / Architecture

- **Clean separation of layers**: Data layer updated to use Room entities and DAOs. Domain logic encapsulated in use cases.
- **Product and receipt linking**: `receiptId` now binds products to receipts.
- **Color management updated**: `ColorPalette` refactored to return Compose `Color` objects directly.

### 🔧 Configuration

- Integrated `receiptlist` module into navigation and DI (Koin).
- Added necessary Compose and Ktor dependencies to new modules.
- Reintroduced temporary Compose dependency to `data` layer due to color conversion issues (marked as TODO).

## [Unreleased] - 19.07.2025
### Refactored

- Use `Long` for color values in the data layer to avoid direct dependency on Compose UI.
- Added extension to convert `List<Long>` to Compose `Color` list in the shared module.
- Updated `DatabaseInitializer` to use `Long` color values.
- Modified `CategoriesViewModel` to initialize and convert colors properly for UI.
- Preserved `categories` and `types` state in `UiStateManager.clearState()` in add receipt feature.
- Cleaned up Gradle build files by removing unnecessary Compose dependencies from the data module and confirming dependencies in feature/home.

## [Unreleased] - 19.07.2025
### Refactored

- **AI Receipt Parsing aligned with Clean Architecture**
    - Introduced `OpenAIService` and `AIRepository` interfaces in the `core` module.
    - Added domain models for AI communication (`OpenAiRequest`, `OpenAiResponse`, `ParsedReceiptData`, `ParsedProductData`, etc.).
    - Implemented `ParseReceiptUseCase` to encapsulate receipt parsing logic.
    - Added custom exceptions for AI-related errors (`ParseReceiptException`, `NetworkException`, etc.).
    - `data` module: Added `OpenAIServiceImpl`, `AIRepositoryImpl`, `OpenAIRequestBuilder`, and `OpenAIMapper`.
    - Updated `OpenAiManager` in `feature/addreceipt` to use `ParseReceiptUseCase`.
    - Koin modules updated to provide all new AI-related services and clients.
    - Added long-timeout `openAIHttpClient` for OpenAI API calls.
    - Minor build config updates (e.g., Compose `Color` TODO in `DatabaseInitializer.kt`).

- **Core Module introduced for domain logic**
    - Added new `core` module to centralize domain models and interfaces.
    - Migrated models: `Category`, `Type`, `Product`, `Receipt`, and `CategoryType`.
    - Moved repository interfaces (`CategoriesTypesRepository`, `ReceiptRepository`) to `core`.
    - Relocated `GetTypesAndCategoriesUseCase` from `feature/addreceipt` to `core`.
    - Updated dependencies in `data`, `di`, `feature/addreceipt`, and `feature/categories` to use `core`.
    - Removed obsolete `CategoriesTypesImpl.kt`.
    - Adjusted imports and usage across ViewModels, managers, and components.
    - Updated build files and `settings.gradle.kts` to include `core`.

- **OpenAIService now receives injected Categories/Types**
    - Injected `CategoriesTypesRepository` into `OpenAiService` to dynamically fetch categories and types.
    - Changed model from `gpt-4o-2024-08-06` to `o4-mini`.
    - Updated OpenAI prompt format to use semicolons as separators.
    - Updated Koin bindings for new repository injection.
