package org.example.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import org.example.addReceipt.AddReceiptScreen
import org.example.category.CategoriesScreen
import org.example.home.HomeGraphScreen
import org.example.receiptlist.ReceiptListScreen
import org.example.shared.navigation.Screen

@Composable
fun SetupNavGraph(startDestination: Screen /* = Screen.HomeGraph For future auth*/) {
    val navController = rememberNavController()
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        composable<Screen.HomeGraph> {
            HomeGraphScreen(
                navigateToCategory = {
                    navController.navigate(Screen.Category)
                },
                navigateToAddReceipt = {
                    navController.navigate(Screen.AddReceipt)
                },
                navigateToAddReceiptWithScan = {
                    navController.navigate(Screen.AddReceiptWithScan)
                },
                navigateToEditReceipt = { receiptId ->
                    navController.navigate(Screen.EditReceipt(receiptId))
                }
            )
        }
        composable<Screen.AddReceipt> {
            AddReceiptScreen(
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }

        composable<Screen.AddReceiptWithScan> {
            AddReceiptScreen(
                navigateBack = {
                    navController.navigateUp()
                },
                startWithScan = true
            )
        }

        composable<Screen.EditReceipt> { backStackEntry ->
            val editReceipt = backStackEntry.toRoute<Screen.EditReceipt>()
            AddReceiptScreen(
                receiptId = editReceipt.receiptId,
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }

        // Drawer screens
        composable<Screen.Category> {
            CategoriesScreen(
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }
    }
}