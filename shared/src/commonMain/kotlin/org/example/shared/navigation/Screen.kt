package org.example.shared.navigation

import kotlinx.serialization.Serializable

@Serializable
sealed class Screen {
    @Serializable
    data object HomeGraph : Screen()

    @Serializable
    data object AddReceipt : Screen()

    @Serializable
    data object AddReceiptWithScan : Screen()

    @Serializable
    data class EditReceipt(val receiptId: String) : Screen()

    @Serializable
    data object Dashboard : Screen()

    @Serializable
    data object ReceiptList : Screen()

    //Drawer screens
    @Serializable
    data object Category : Screen()
}